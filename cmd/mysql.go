package main

import (
	"database/sql"
	"fmt"

	_ "github.com/go-sql-driver/mysql"
)

type MySQLConfig struct {
	User     string
	Password string
	Socket   string
}

// 使用socket方式创建MySQL简单连接
func conn(user, password, socket string) (*sql.DB, error) {
	// 连接字符串：用户名:密码@unix(UnixSocket路径)/ (不指定数据库)
	dsn := fmt.Sprintf("%s:%s@unix(%s)/", user, password, socket)

	// 打开数据库连接
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}
	fmt.Println("Connected to MySQL via Unix Socket successfully!")

	return db, nil
}

func Show(cfg *MySQLConfig, key string) error {
	db, err := conn(cfg.User, cfg.Password, cfg.Socket)
	if err != nil {
		return err
	}
	defer db.Close()

	// 执行查询（示例）
	// 注意：SHOW 语句不支持占位符，需要直接拼接字符串
	query := fmt.Sprintf("SHOW VARIABLES LIKE '%s'", key)
	rows, err := db.Query(query)
	if err != nil {
		return fmt.Errorf("query failed: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var name string
		var value string
		if err := rows.Scan(&name, &value); err != nil {
			return fmt.Errorf("failed to scan row: %w", err)
		}
		fmt.Printf("Name: %s, Value: %s\n", name, value)
	}

	return nil
}

func main() {
	err := Show(&MySQLConfig{
		User:     "root",
		Password: "_Y5%C2wncJC6b^frHdiEKw*kn05VNN",
		Socket:   "/home/<USER>/mysql/tmp/mysql.sock",
	}, "slave_parallel_workers")
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println("done")
}
