package utils

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"
)

type Retry struct {
	MaxRetries    int           // 最大重试次数
	EnableBackoff bool          // 是否启用指数退避
	BaseDelay     time.Duration // 基础延迟时间
	MaxDelay      time.Duration // 指数退避最大延迟时间
	Multiplier    float64       // 延迟倍数
	Jitter        bool          // 是否添加随机抖动
}

type Shell struct {
	Command string
	Args    []string
	Timeout time.Duration
	Retry   *Retry // 新增：高级重试配置
}

// 执行命令行程序
func execCommand(cmd *Shell) (string, error) {
	// 超时默认值
	if cmd.Timeout == 0 {
		cmd.Timeout = 3 * time.Second
	}
	// 重试默认值
	if cmd.Retry == nil {
		cmd.Retry = &Retry{MaxRetries: 1}
	}
	if cmd.Retry.MaxRetries == 0 {
		cmd.Retry.MaxRetries = 1
	}
	if cmd.Retry.EnableBackoff {
		if cmd.Retry.BaseDelay == 0 {
			cmd.Retry.BaseDelay = 100 * time.Millisecond
		}
		if cmd.Retry.MaxDelay == 0 {
			cmd.Retry.MaxDelay = 30 * time.Second
		}
		if cmd.Retry.Multiplier == 0 {
			cmd.Retry.Multiplier = 2.0
		}
	}

	// 执行
	var output []byte
	var err error
	for i := 0; i < cmd.Retry.MaxRetries; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), cmd.Timeout)
		output, err = exec.CommandContext(ctx, cmd.Command, cmd.Args...).CombinedOutput()
		cancel()
		// 成功直接返回
		if err == nil {
			break
		}
		// 最大次数返回
		if i == cmd.Retry.MaxRetries {
			break
		}

		// 计算并等待退避延迟
		if cmd.Retry.EnableBackoff {
			delay := backoffDelay(i, cmd.Retry)
			time.Sleep(delay)
		}
	}

	// 增强的错误信息
	if exitError, ok := err.(*exec.ExitError); ok {
		return string(output), fmt.Errorf("command failed with exit code %d: %w, stderr: %s",
			exitError.ExitCode(), err, string(exitError.Stderr))
	}
	// 检查是否是超时错误
	if errors.Is(err, context.DeadlineExceeded) {
		return string(output), fmt.Errorf("command execution timeout: %w", err)
	}

	return string(output), fmt.Errorf("command execution failed: %w", err)
}

// backoffDelay 计算指数退避延迟时间
func backoffDelay(attempt int, config *Retry) time.Duration {
	// 基础倍率
	delay := time.Duration(float64(config.BaseDelay) * math.Pow(config.Multiplier, float64(attempt)))

	// 添加 ±25% 的随机抖动
	if config.Jitter {
		jitterRange := float64(delay) * 0.25
		jitter := (rand.Float64() - 0.5) * 2 * jitterRange
		delay = time.Duration(float64(delay) + jitter)
	}

	// 确保在base ~ max 之间
	return min(max(delay, config.BaseDelay), config.MaxDelay)
}

var ExecCommand func(*Shell) (string, error) = execCommand

// ==========================================
//                  Mock
// ==========================================

var mockedExitStatus chan int
var mockedStdout chan string

func ActivateExecMock(cmdNum ...int) {
	num := 10
	if len(cmdNum) > 0 {
		num = cmdNum[0]
	}
	mockedExitStatus = make(chan int, num)
	mockedStdout = make(chan string, num)
	ExecCommand = fakeExecCommand
}

func DeactivateExecMock() {
	close(mockedExitStatus)
	close(mockedStdout)
	ExecCommand = execCommand
}

func ExpectExec(status int, stdout string) {
	mockedExitStatus <- status
	mockedStdout <- stdout
}

// 用于mock的假函数
func fakeExecCommand(cfg *Shell) (string, error) {
	cs := []string{"-test.run=TestExecCommandHelper", "--", cfg.Command}
	cs = append(cs, cfg.Args...)
	cmd := exec.Command(os.Args[0], cs...)
	select {
	case status := <-mockedExitStatus:
		es := strconv.Itoa(status)
		cmd.Env = []string{"GO_WANT_HELPER_PROCESS=1", "STDOUT=" + (<-mockedStdout), "EXIT_STATUS=" + es}
	default:
		return "", fmt.Errorf("unexpected exec command %s", cfg.Command)
	}

	stdout, err := cmd.CombinedOutput()
	output := strings.ReplaceAll(string(stdout), "warning: GOCOVERDIR not set, no coverage data emitted\n", "")

	// 增强的错误信息
	if exitError, ok := err.(*exec.ExitError); ok {
		return string(output), fmt.Errorf("command failed with exit code %d: %w, stderr: %s",
			exitError.ExitCode(), err, string(exitError.Stderr))
	}
	// 检查是否是超时错误
	if errors.Is(err, context.DeadlineExceeded) {
		return string(output), fmt.Errorf("command execution timeout: %w", err)
	}

	return string(output), fmt.Errorf("command execution failed: %w", err)
}
