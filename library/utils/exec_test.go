package utils

import (
	"fmt"
	"os"
	"strconv"
	"testing"
	"time"
)

// 测试指数退避重试
func Test_backoffDelay(t *testing.T) {
	type args struct {
		attempt int
		config  *Retry
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(t *testing.T, d time.Duration)
	}{
		{
			name:   "test1",
			before: func() {},
			args: args{
				attempt: 2,
				config: &Retry{
					Multiplier: 2.0,
					BaseDelay:  50 * time.Millisecond, // 使用较小的延迟以加快测试
					MaxDelay:   3 * time.Second,
					Jitter:     false, // 测试时关闭抖动
				},
			},
			expect: func(t *testing.T, d time.Duration) {
				if d < 50*time.Millisecond {
					t.<PERSON>rf("Expected at least %v delay, got %v", 50*time.Millisecond, d)
				}
				if d > 3*time.Second {
					t.Errorf("Expected at most %v delay, got %v", 3*time.Second, d)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			d := backoffDelay(tt.args.attempt, tt.args.config)
			if tt.expect != nil {
				tt.expect(t, d)
			}
		})
	}
}

// 测试执行命令
func Test_execCommand(t *testing.T) {
	type args struct {
		cmd *Shell
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(t *testing.T, output string, err error)
	}{
		{
			name:   "test timeout",
			before: func() {},
			args: args{
				cmd: &Shell{
					Command: "sleep",
					Args:    []string{"2"},
					Timeout: 1 * time.Second,
				},
			},
			expect: func(t *testing.T, output string, err error) {
				if err == nil {
					t.Errorf("expected error, got %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			output, err := execCommand(tt.args.cmd)
			if tt.expect != nil {
				tt.expect(t, output, err)
			}
		})
	}
}

func TestMockExecCommand(t *testing.T) {
	ActivateExecMock()
	ExpectExec(0, "hhhh")

	output, err := ExecCommand(&Shell{
		Command: "netstat",
		Args:    []string{"-anlt | grep ESTABLISHED"},
		Timeout: 2 * time.Second,
	})
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if string(output) != "hhhh" {
		t.Errorf("Expected %q, got %q", "hhhh", string(output))
	}
}

func TestExecCommandHelper(t *testing.T) {
	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {
		return
	}

	// println("Mocked stdout:", os.Getenv("STDOUT"))
	fmt.Fprint(os.Stdout, os.Getenv("STDOUT"))
	i, _ := strconv.Atoi(os.Getenv("EXIT_STATUS"))
	os.Exit(i)
}
