/**
 *	xtrabackup 备份
 *	根据给定的Backup Options，启动对应集群在本机的实例备份
 *	- 同一个集群在本机只能跑一个备份任务，不论是全备还是增倍，当一个备份任务没跑完时，后来的备份任务都不能启动
 *  - 支持ssh远程流式传输
 *  - 支持超时控制、重试，支持主动停止
 *  - 备份任务完成后返回备份结果，包括：启动时间、完成时间、LSN信息等
 */
package xtrabackup

import (
	"bufio"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"mdc-agent/library/logger"
	"mdc-agent/library/utils"
)

var (
	lockMap sync.Map // 同一个集群同时只能在本机跑一个备份
)

// BackupType 备份类型
type BackupType int

const (
	BackupTypeFull        BackupType = iota // 全量备份
	BackupTypeIncremental                   // 增量备份
)

// BackupResult 备份结果
type BackupResult struct {
	StartTime time.Time // 开始时间
	EndTime   time.Time // 结束时间
	LSNFrom   string    // 起始 LSN
	LSNTo     string    // 结束 LSN
}

// BackupOptions 备份选项
type BackupOptions struct {
	Cluster         string     // 集群，用于加锁
	DefaultFile     string     // 默认配置文件路径
	Socket          string     // socket 文件路径
	User            string     // MySQL 用户名
	Password        string     // MySQL 密码
	UseMemory       string     // 使用内存
	CompressThreads int        // 压缩线程数
	Parallel        int        // 并行线程数
	TargetDir       string     // 目标目录
	ExtraLSNDir     string     // 额外 LSN 目录
	ExtraLSNFile    string     // 额外 LSN 文件
	Type            BackupType // 备份类型
	LSNPosition     string     // LSN 位置（增量备份用）

	Throttle             int    // 限速
	KillLongQueryType    string // 杀死长查询类型
	KillLongQueryTimeout int    // 杀死长查询超时时间

	Stream         bool   // 是否流式备份
	RemoteTransfer bool   // 远程传输，只能在流式备份的情况使用
	RemoteHost     string // 远程主机
	RemotePath     string // 远程路径
}

// 实例备份
func Backup(options *BackupOptions) (*BackupResult, error) {
	// 1、参数校验&初始化
	err := backupArgsValidate(options)
	if err != nil {
		return nil, err
	}

	// 2、同一个集群同时只能跑一个备份，检查lockMap中是否存在锁
	process := Process{}
	if _, loaded := lockMap.LoadOrStore(options.Cluster, &process); loaded {
		errMsg := fmt.Sprintf("backup task is already running for cluster %s", options.Cluster)
		logger.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	defer lockMap.Delete(options.Cluster)

	// 3、关闭并行复制 & defer恢复
	workers, err := DisableParallelReplication(options)
	if err != nil {
		return nil, err
	}
	defer EnableParallelReplication(options, workers)

	// 4、启动xtrabackup备份
	err = backupExecCommand(options)
	if err != nil {
		return nil, err
	}

	// 5、解析备份结果
	result, err := backupResult(options.ExtraLSNDir)
	if err != nil {
		logger.Warn("failed to parse backup result, error=(%v)", err)
		return nil, err
	}

	return result, nil
}

// 备份参数检查 & 初始化
func backupArgsValidate(options *BackupOptions) error {
	if options.Cluster == "" {
		return fmt.Errorf("cluster name can not be empty")
	}
	if options.DefaultFile == "" {
		return fmt.Errorf("default file can not be empty")
	}
	if options.Socket == "" {
		return fmt.Errorf("socket can not be empty")
	}
	if options.User == "" || options.Password == "" {
		return fmt.Errorf("user or password can not be empty")
	}

	// 远程传输
	if options.RemoteTransfer {
		// 必须是流式传输
		if !options.Stream {
			return fmt.Errorf("remote data transfer requires stream mode")
		}
		// 需要指明宿主机和目录
		if options.RemoteHost == "" {
			return fmt.Errorf("remote host can not be empty when remote transfer")
		}
		if options.RemotePath == "" {
			return fmt.Errorf("remote path can not be empty when remote transfer")
		}
	}
	// 增量备份需要指定LSN点位
	if options.Type == BackupTypeIncremental && options.LSNPosition == "" {
		return fmt.Errorf("lsn position can not be empty when incremental backup")
	}
	// 默认长事务处理参数
	if options.KillLongQueryType == "" {
		options.KillLongQueryType = "select"
	}
	if options.KillLongQueryTimeout == 0 {
		options.KillLongQueryTimeout = 20
	}
	if options.Throttle == 0 {
		options.Throttle = 500
	}
	if options.UseMemory == "" {
		options.UseMemory = useMemory
	}

	return nil
}

// ./xtrabackup --defaults-file=/home/<USER>/mysql/etc/my.cnf \
// --user=root --password='_Y5%C2wncJC6b^frHdiEKw*kn05VNN' \
// --socket=/home/<USER>/mysql/tmp/mysql.sock \
// --backup \
// --compress \
// --compress-threads=4 \
// --parallel=16 \
// --slave-info \
// --lock-ddl-per-table \
// --stream=xbstream \
// --incremental-lsn=21075002180 \
// --extra-lsndir=/home/<USER>/backups/backup-incr-21075002180-lsn \
// 2> /home/<USER>/backups/backup-incr-$(date +'%F') \
// | ssh work@10.32.162.80 "cat > /home/<USER>/backups/backup-incr-21075002180.xbstream"

// backupExecCommand 执行备份
func backupExecCommand(options *BackupOptions) error {
	// 构建命令
	cmdArgs := []string{}

	// 基础参数
	cmdArgs = append(cmdArgs, fmt.Sprintf("--defaults-file=%s/etc/my.cnf", options.DefaultFile))
	cmdArgs = append(cmdArgs, fmt.Sprintf("--socket=%s", options.Socket))
	cmdArgs = append(cmdArgs, fmt.Sprintf("--user=%s", options.User))
	cmdArgs = append(cmdArgs, fmt.Sprintf("--password=\"%s\"", options.Password))
	cmdArgs = append(cmdArgs, "--backup")

	// 压缩
	cmdArgs = append(cmdArgs, "--slave-info")
	cmdArgs = append(cmdArgs, "--lock-ddl-per-table")
	cmdArgs = append(cmdArgs, "--compress", fmt.Sprintf("--compress-threads=%d", options.CompressThreads))
	cmdArgs = append(cmdArgs, fmt.Sprintf("--kill-long-query-type=%s", options.KillLongQueryType))
	cmdArgs = append(cmdArgs, fmt.Sprintf("--kill-long-queries-timeout=%d", options.KillLongQueryTimeout))

	// 限速选项
	if options.Throttle > 0 {
		cmdArgs = append(cmdArgs, fmt.Sprintf("--throttle=%d", options.Throttle))
	}

	// 增备
	if options.Type == BackupTypeIncremental {
		cmdArgs = append(cmdArgs, "--incremental")
		cmdArgs = append(cmdArgs, fmt.Sprintf("--incremental-lsn=%s", options.LSNPosition))
	}

	// 额外输出目录
	if options.TargetDir != "" {
		cmdArgs = append(cmdArgs, options.TargetDir)
	}

	// LSN 目录
	if options.ExtraLSNDir != "" {
		cmdArgs = append(cmdArgs, fmt.Sprintf("--extra-lsndir=%s", options.ExtraLSNDir))
	}

	// 流式传输
	if options.Stream {
		cmdArgs = append(cmdArgs, "--stream=xbstream")
	}

	// 日志文件留存，日志文件命名规范：集群名/backup-备份类型-备份时间.log
	cmdArgs = append(cmdArgs, fmt.Sprintf("2> %s/%s/backup-%d-%s.log", logPath, options.Cluster, options.Type, time.Now().Format("20060102150405")))

	cmd := utils.Shell{}
	// SSH流式远程传输，需要使用管道，用bash命令
	if options.RemoteTransfer {
		cmdArgs = append(cmdArgs, fmt.Sprintf("| ssh mysql@%s \"cat > %s\"", options.RemoteHost, options.RemotePath))
		cmdArgs = append([]string{binXtrabackup}, cmdArgs...)

		cmd.Command = "bash"
		cmd.Args = []string{"-c", strings.Join(cmdArgs, " ")}
	} else {
		cmd.Command = binXtrabackup
		cmd.Args = cmdArgs
	}

	// 执行
	_, err := utils.ExecCommand(&cmd)
	if err != nil {
		return err
	}

	return nil
}

// uuid = 1202827a-8d4a-11f0-aab8-fa2700000477
// name =
// tool_name = xtrabackup
// tool_command = --defaults-file=/home/<USER>/mysql/etc/my.cnf --user=root --password=... --socket=/home/<USER>/mysql/tmp/mysql.sock --backup --compress --compress-threads=4 --parallel=16 --slave-info --lock-ddl-per-table --stream=xbstream --incremental-lsn=21075002180 --extra-lsndir=/home/<USER>/backups/backup-incr-21075002180-lsn
// tool_version = 2.4.15
// ibbackup_version = 2.4.15
// server_version = 5.7.25-log
// start_time = 2025-09-09 14:56:07
// end_time = 2025-09-09 14:56:11
// lock_time = 0
// binlog_pos = filename 'mysql-bin.000031', position '219416882', GTID of the last change 'c9d897f9-fe94-11ed-9852-fa2700000477:1-25239555'
// innodb_from_lsn = 21075002180
// innodb_to_lsn = 21101453281
// partial = N
// incremental = Y
// format = xbstream
// compact = N
// compressed = compressed
// encrypted = N

// 整理备份结果
func backupResult(extraLSNDir string) (*BackupResult, error) {
	// 读文件
	file, err := os.Open(filepath.Join(extraLSNDir, "xtrabackup_info"))
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// 逐行解析
	info := &BackupResult{}
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		if scanner.Text() == "" {
			continue
		}

		line := scanner.Text()
		switch true {
		case strings.HasPrefix(line, "start_time ="):
			info.StartTime, err = time.Parse("2006-01-02 15:04:05", strings.Split(line, " = ")[1])
			if err != nil {
				return nil, err
			}
		case strings.HasPrefix(line, "end_time ="):
			info.EndTime, err = time.Parse("2006-01-02 15:04:05", strings.Split(line, " = ")[1])
			if err != nil {
				return nil, err
			}
		case strings.HasPrefix(line, "innodb_from_lsn ="):
			info.LSNFrom = strings.Split(line, " = ")[1]
		case strings.HasPrefix(line, "innodb_to_lsn ="):
			info.LSNTo = strings.Split(line, " = ")[1]
		}
	}

	return info, nil
}
