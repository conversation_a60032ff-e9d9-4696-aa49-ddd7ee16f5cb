package xtrabackup

import (
	"fmt"
	"os"
	"regexp"
	"time"
)

// NewRateLimiter 创建限速控制器
func NewRateLimiter(rate int64) *RateLimiter {
	return &RateLimiter{
		rate:    rate,
		enabled: rate > 0,
	}
}

// SetRate 设置限速
func (r *RateLimiter) SetRate(rate int64) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.rate = rate
	r.enabled = rate > 0
}

// GetRate 获取当前限速
func (r *RateLimiter) GetRate() int64 {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.rate
}

// IsEnabled 检查是否启用限速
func (r *RateLimiter) IsEnabled() bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.enabled
}

// NewProgressMonitor 创建进度监控器
func NewProgressMonitor() *ProgressMonitor {
	return &ProgressMonitor{
		info: &ProgressInfo{
			Status:   StatusIdle,
			Progress: 0,
		},
	}
}

// SetStatus 设置状态
func (p *ProgressMonitor) SetStatus(status BackupStatus) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.info.Status = status
	p.info.Message = p.getStatusMessage(status)

	if p.callback != nil {
		p.callback(p.info)
	}
}

// SetProgress 设置进度
func (p *ProgressMonitor) SetProgress(progress float64) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.info.Progress = progress

	if p.callback != nil {
		p.callback(p.info)
	}
}

// GetProgress 获取进度信息
func (p *ProgressMonitor) GetProgress() *ProgressInfo {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	// 返回副本避免并发问题
	return &ProgressInfo{
		Status:      p.info.Status,
		Progress:    p.info.Progress,
		CurrentFile: p.info.CurrentFile,
		ProcessedMB: p.info.ProcessedMB,
		TotalMB:     p.info.TotalMB,
		Speed:       p.info.Speed,
		ETA:         p.info.ETA,
		Message:     p.info.Message,
	}
}

// SetCallback 设置进度回调函数
func (p *ProgressMonitor) SetCallback(callback func(*ProgressInfo)) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.callback = callback
}

// getStatusMessage 获取状态消息
func (p *ProgressMonitor) getStatusMessage(status BackupStatus) string {
	switch status {
	case StatusIdle:
		return "Ready to start backup"
	case StatusRunning:
		return "Backup in progress"
	case StatusPaused:
		return "Backup paused"
	case StatusCompleted:
		return "Backup completed successfully"
	case StatusFailed:
		return "Backup failed"
	case StatusStopped:
		return "Backup stopped"
	default:
		return "Unknown status"
	}
}

// NewProcessManager 创建进程管理器
func NewProcessManager() *ProcessManager {
	return &ProcessManager{}
}

// SetPID 设置进程 ID
func (p *ProcessManager) SetPID(pid int) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.pid = pid
}

// GetPID 获取进程 ID
func (p *ProcessManager) GetPID() int {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.pid
}

// SetCommand 设置执行命令
func (p *ProcessManager) SetCommand(cmd string) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.cmd = cmd
	p.startTime = time.Now()
}

// GetCommand 获取执行命令
func (p *ProcessManager) GetCommand() string {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.cmd
}

// GetStartTime 获取启动时间
func (p *ProcessManager) GetStartTime() time.Time {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.startTime
}
