package xtrabackup

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"syscall"
	"time"

	"mdc-agent/library/logger"
	"mdc-agent/library/utils"
)

// Process 进程管理
type Process struct {
	pid       int       // 进程 ID
	cmd       string    // 执行命令
	startTime time.Time // 启动时间
}

// 检查备份进程是否存在
func checkProcess() (bool, error) {
	// // 检查 innobackupex 进程
	// cmd := fmt.Sprintf("ps -ef | grep \"innobackupex --defaults-file=%s/etc/my.cnf\" | grep -v grep | awk '{print $2}'",
	// 	m.config.MySQLBaseDir)

	// output, err := utils.ExecCommand(&utils.Shell{
	// 	Command: "bash",
	// 	Args:    []string{"-c", cmd},
	// 	Timeout: 5 * time.Second,
	// 	Retry:   &utils.Retry{MaxRetries: 3},
	// })
	// if err != nil {
	// 	return false, fmt.Errorf("failed to check backup process: %w", err)
	// }

	// pid := strings.TrimSpace(output)
	// if pid == "" {
	// 	return false, nil
	// }

	// // 验证进程是否真的存在
	// if pidInt, err := strconv.Atoi(pid); err == nil {
	// 	if process, err := os.FindProcess(pidInt); err == nil {
	// 		// 发送信号 0 检查进程是否存在
	// 		if err := process.Signal(syscall.Signal(0)); err == nil {
	// 			m.processManager.SetPID(pidInt)
	// 			return true, nil
	// 		}
	// 	}
	// }

	return false, nil
}

// KillProcess 杀死备份进程
func KillProcess() error { return nil }

// Stop 停止备份
func Stop(cluster string) error { return nil }

// Pause 暂停备份
func (m *XtraBackupManager) Pause() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.status != StatusRunning {
		return fmt.Errorf("backup is not running, current status: %v", m.status)
	}

	// 创建暂停文件
	if err := m.createPauseFile(); err != nil {
		return fmt.Errorf("failed to create pause file: %w", err)
	}

	m.status = StatusPaused
	m.monitor.SetStatus(StatusPaused)

	logger.Info("Backup paused successfully")
	return nil
}

// Resume 恢复备份
func (m *XtraBackupManager) Resume() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.status != StatusPaused {
		return fmt.Errorf("backup is not paused, current status: %v", m.status)
	}

	// 删除暂停文件
	if err := m.removePauseFile(); err != nil {
		return fmt.Errorf("failed to remove pause file: %w", err)
	}

	m.status = StatusRunning
	m.monitor.SetStatus(StatusRunning)

	logger.Info("Backup resumed successfully")
	return nil
}

// Stop 停止备份
func (m *XtraBackupManager) Stop() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.status == StatusIdle || m.status == StatusCompleted {
		return nil
	}

	// 杀死备份进程
	if err := m.KillProcess(); err != nil {
		logger.Warn("Failed to kill backup process: %v", err)
	}

	// 清理暂停文件
	m.removePauseFile()

	m.status = StatusStopped
	m.monitor.SetStatus(StatusStopped)

	logger.Info("Backup stopped successfully")
	return nil
}

// SetThrottleRate 设置限速
func (m *XtraBackupManager) SetThrottleRate(rate int64) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 更新配置
	m.config.ThrottleRate = rate
	m.limiter.SetRate(rate)

	// 如果备份正在运行，动态调整限速
	if m.status == StatusRunning {
		if err := m.updateRunningBackupThrottle(rate); err != nil {
			logger.Warn("Failed to update running backup throttle: %v", err)
			// 不返回错误，因为配置已更新，下次备份会生效
		}
	}

	logger.Info("Throttle rate updated to %d MB/s", rate)
	return nil
}

// GetStatus 获取当前状态
func (m *XtraBackupManager) GetStatus() BackupStatus {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.status
}

// GetProgress 获取进度信息
func (m *XtraBackupManager) GetProgress() *ProgressInfo {
	return m.monitor.GetProgress()
}

// IsRunning 检查是否正在运行
func (m *XtraBackupManager) IsRunning() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.status == StatusRunning || m.status == StatusPaused
}

// CheckProcess 检查备份进程是否存在
func (m *XtraBackupManager) CheckProcess() (bool, error) {
	// 检查 innobackupex 进程
	cmd := fmt.Sprintf("ps -ef | grep \"innobackupex --defaults-file=%s/etc/my.cnf\" | grep -v grep | awk '{print $2}'",
		m.config.MySQLBaseDir)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		Timeout: 5 * time.Second,
		Retry:   &utils.Retry{MaxRetries: 3},
	})
	if err != nil {
		return false, fmt.Errorf("failed to check backup process: %w", err)
	}

	pid := strings.TrimSpace(output)
	if pid == "" {
		return false, nil
	}

	// 验证进程是否真的存在
	if pidInt, err := strconv.Atoi(pid); err == nil {
		if process, err := os.FindProcess(pidInt); err == nil {
			// 发送信号 0 检查进程是否存在
			if err := process.Signal(syscall.Signal(0)); err == nil {
				m.processManager.SetPID(pidInt)
				return true, nil
			}
		}
	}

	return false, nil
}

// KillProcess 杀死备份进程
func (m *XtraBackupManager) KillProcess() error {
	// 首先尝试优雅停止
	if err := m.gracefulStop(); err != nil {
		logger.Warn("Graceful stop failed, using force kill: %v", err)
		return m.forceKill()
	}
	return nil
}

// createPauseFile 创建暂停文件
func (m *XtraBackupManager) createPauseFile() error {
	file, err := os.Create(m.pauseFile)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入暂停时间
	_, err = file.WriteString(fmt.Sprintf("paused_at=%d\n", time.Now().Unix()))
	return err
}

// removePauseFile 删除暂停文件
func (m *XtraBackupManager) removePauseFile() error {
	if _, err := os.Stat(m.pauseFile); os.IsNotExist(err) {
		return nil
	}
	return os.Remove(m.pauseFile)
}

// updateRunningBackupThrottle 更新运行中备份的限速
func (m *XtraBackupManager) updateRunningBackupThrottle(rate int64) error {
	// 注意：XtraBackup 不支持运行时动态调整限速
	// 这里只是记录新的限速值，实际生效需要重启备份
	logger.Info("Throttle rate will take effect on next backup: %d MB/s", rate)
	return nil
}

// gracefulStop 优雅停止备份进程
func (m *XtraBackupManager) gracefulStop() error {
	pid := m.processManager.GetPID()
	if pid <= 0 {
		return fmt.Errorf("no valid process ID")
	}

	process, err := os.FindProcess(pid)
	if err != nil {
		return fmt.Errorf("failed to find process: %w", err)
	}

	// 发送 SIGTERM 信号
	if err := process.Signal(syscall.SIGTERM); err != nil {
		return fmt.Errorf("failed to send SIGTERM: %w", err)
	}

	// 等待进程结束
	// 注意：这里应该有超时机制，但为了简化代码暂时省略
	_, err = process.Wait()
	return err
}

// forceKill 强制杀死备份进程
func (m *XtraBackupManager) forceKill() error {
	cmd := "ps -ef | grep \"innobackupex\" | grep -v grep | awk '{print $2}' | xargs -r kill -9"
	_, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		Timeout: 5 * time.Second,
		Retry: &utils.Retry{
			MaxRetries: 3,
		},
	})
	if err != nil {
		return fmt.Errorf("failed to force kill backup process: %w", err)
	}

	logger.Info("Backup process force killed")
	return nil
}
